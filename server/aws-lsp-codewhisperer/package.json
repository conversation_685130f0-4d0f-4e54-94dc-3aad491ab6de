{"name": "@aws/lsp-codewhisperer", "version": "0.0.44", "description": "CodeWhisperer Language Server", "main": "out/index.js", "repository": {"type": "git", "url": "https://github.com/aws/language-servers"}, "author": "Amazon Web Services", "license": "Apache-2.0", "engines": {"node": ">=18.0.0"}, "scripts": {"compile": "tsc --build", "postcompile": "npm run copyServiceClient", "copyServiceClient": "copyfiles -u 1 --error ./src/client/sigv4/*.json out && copyfiles -u 1 --error ./src/client/token/*.json out", "fix": "npm run fix:prettier", "fix:prettier": "prettier . --write", "lint": "npm run lint:src", "lint:bundle:webworker": "webpack --config webpack.lint.config.js && eslint bundle/aws-lsp-codewhisperer-webworker.js # Verify compatibility with web runtime target", "lint:src": "eslint src/ --ext .ts,.tsx", "test:unit": "ts-mocha --timeout 0 -b \"./src/**/*.test.ts\"", "test": "npm run lint && npm run test:unit", "prepack": "npm run compile && ts-node ../../script/link_bundled_dependencies.ts", "postinstall": "node ./script/install_transitive_dep.js"}, "dependencies": {"@amzn/amazon-q-developer-streaming-client": "file:../../core/q-developer-streaming-client/amzn-amazon-q-developer-streaming-client-1.0.0.tgz", "@amzn/codewhisperer-streaming": "file:../../core/codewhisperer-streaming/amzn-codewhisperer-streaming-1.0.7.tgz", "@aws-sdk/util-arn-parser": "^3.723.0", "@aws-sdk/util-retry": "^3.374.0", "@aws/chat-client-ui-types": "^0.1.40", "@aws/language-server-runtimes": "^0.2.90", "@aws/lsp-core": "^0.0.9", "@modelcontextprotocol/sdk": "^1.9.0", "@smithy/node-http-handler": "^2.5.0", "adm-zip": "^0.5.10", "archiver": "^7.0.1", "aws-sdk": "^2.1403.0", "axios": "^1.8.4", "chokidar": "^4.0.3", "deepmerge": "^4.3.1", "diff": "^7.0.0", "fast-glob": "^3.3.3", "fastest-levenshtein": "^1.0.16", "fdir": "^6.4.3", "fuse.js": "^7.1.0", "got": "^11.8.5", "hpagent": "^1.2.0", "ignore": "^7.0.3", "js-md5": "^0.8.3", "jszip": "^3.10.1", "lokijs": "^1.5.12", "picomatch": "^4.0.2", "shlex": "2.1.2", "uuid": "^11.0.5", "vscode-uri": "^3.1.0", "ws": "^8.18.0", "xml2js": "^0.6.2", "xmlbuilder2": "^3.1.1"}, "devDependencies": {"@types/adm-zip": "^0.5.5", "@types/archiver": "^6.0.2", "@types/diff": "^7.0.2", "@types/local-indexing": "file:./types/types-local-indexing-1.1.0.tgz", "@types/lokijs": "^1.5.14", "@types/uuid": "^9.0.8", "@types/xml2js": "^0.4.14", "assert": "^2.1.0", "copyfiles": "^2.4.1", "mock-fs": "^5.2.0", "sinon": "^19.0.2", "ts-loader": "^9.4.4", "ts-mocha": "^11.1.0", "ts-sinon": "^2.0.2", "vscode-languageserver-textdocument": "^1.0.11", "webpack": "^5.94.0", "webpack-cli": "^6.0.1"}, "prettier": {"printWidth": 120, "trailingComma": "es5", "tabWidth": 4, "singleQuote": true, "semi": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf"}, "bundleDependencies": ["@amzn/codewhisperer-streaming", "@amzn/amazon-q-developer-streaming-client"]}