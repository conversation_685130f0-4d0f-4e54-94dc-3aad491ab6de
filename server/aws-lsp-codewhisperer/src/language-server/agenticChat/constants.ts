export const genericErrorMsg = 'An unexpected error occurred, check the logs for more information.'
export const loadingThresholdMs = 2000
export const generateAssistantResponseInputLimit = 500_000
export const outputLimitExceedsPartialMsg = 'output exceeds maximum character limit of'
export const responseTimeoutMs = 170_000
export const responseTimeoutPartialMsg = 'Response processing timed out after'
export const clientTimeoutMs = 180_000
